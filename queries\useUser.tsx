import useUserAPI from '~api/useUser';
import { useQuery, UseQueryOptions, QueryKey } from '~node_modules/react-query';
import { User } from '~types';

type Props = {
  options?: Omit<
    UseQueryOptions<User, unknown, User, QueryKey>,
    'queryKey' | 'queryFn'
  >;
};

const useUser = ({ options }: Props = {}) => {
  const { getUser } = useUserAPI();

  const { data, isLoading } = useQuery('user', () => getUser(), options);

  return { data, isLoading };
};

export default useUser;
