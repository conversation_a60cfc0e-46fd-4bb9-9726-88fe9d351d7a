{"main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "test": "jest --watchAll", "prepare": "husky install", "debug": "open \"rndebugger://set-debugger-loc?host=localhost&port=19000\"", "postinstall": "patch-package"}, "jest": {"preset": "jest-expo"}, "resolutions": {"react-async-hook": "^4.0.0", "cliui": "^8.0.0"}, "dependencies": {"@date-fns/tz": "^1.2.0", "@date-fns/utc": "^2.1.0", "@eva-design/eva": "^2.2.0", "@expo/config": "^11.0.0", "@expo/config-plugins": "~10.0.0", "@expo/html-elements": "^0.5.1", "@expo/metro-config": "~0.20.0", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.0.0", "@expo/webpack-config": "~19.0.1", "@futurejj/react-native-visibility-sensor": "^1.3.20", "@mapbox/search-js-core": "^1.1.0", "@mapbox/search-js-react": "^1.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/masked-view": "^0.1.11", "@react-native-community/netinfo": "11.4.1", "@react-native-picker/picker": "2.11.0", "@react-navigation/bottom-tabs": "^6.5.9", "@react-navigation/devtools": "^6.0.19", "@react-navigation/elements": "^1.3.19", "@react-navigation/native": "^6.1.8", "@react-navigation/stack": "^6.3.18", "@sentry/react-native": "~6.14.0", "@types/humps": "^2.0.1", "@types/react-currency-format": "^1.0.0", "@types/react-native-version-check": "^3.4.8", "@ui-kitten/components": "^5.3.1", "@ui-kitten/eva-icons": "^5.3.1", "accordion-collapse-react-native": "^1.1.1", "ahooks": "^3.5.2", "axios": "^0.23.0", "babel-plugin-inline-dotenv": "^1.7.0", "date-fns": "^2.23.0", "date-fns-tz": "^1.3.4", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-react-hooks": "^4.6.0", "expo": "^53.0.11", "expo-application": "~6.1.4", "expo-asset": "~11.1.5", "expo-av": "~15.1.5", "expo-build-properties": "~0.14.6", "expo-clipboard": "~7.1.4", "expo-constants": "~17.1.6", "expo-dev-client": "~5.2.0", "expo-device": "~7.1.4", "expo-document-picker": "~13.1.5", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-location": "~18.1.6", "expo-notifications": "~0.29.13", "expo-secure-store": "~14.2.3", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-updates": "~0.28.14", "expo-video-thumbnails": "~9.1.3", "expo-web-browser": "~14.1.6", "formik": "^2.2.9", "global": "^4.4.0", "humps": "^2.0.1", "libphonenumber-js": "^1.9.44", "lodash": "^4.17.21", "metro": "^0.82.0", "patch-package": "^8.0.0", "pluralize": "^8.0.0", "postinstall-postinstall": "^2.1.0", "react": "19.0.0", "react-async-hook": "^4.0.0", "react-currency-format": "^1.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-calendars": "^1.1286.0", "react-native-country-flag": "^2.0.2", "react-native-country-picker-modal": "^2.0.0", "react-native-gesture-handler": "~2.24.0", "react-native-hyperlink": "^0.0.22", "react-native-image-pan-zoom": "^2.1.12", "react-native-keyboard-aware-scroll-view": "^0.9.4", "react-native-phone-number-input": "^2.1.0", "react-native-purchases": "^8.11.3", "react-native-reanimated": "~3.17.4", "react-native-root-siblings": "^4.1.1", "react-native-root-toast": "^3.2.1", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.4.0", "react-native-svg-transformer": "^1.1.0", "react-native-swiper-flatlist": "^3.0.16", "react-native-timer-picker": "^1.8.2", "react-native-toast-notifications": "^3.4.0", "react-native-vector-icons": "^10.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "react-query": "^3.26.0", "rn-pdf-reader-js": "^4.1.1", "semver": "^7.6.0", "spacetime": "^7.1.0", "timezone-soft": "^1.3.1", "yup": "^0.32.11"}, "devDependencies": {"@babel/core": "^7.19.3", "@types/pluralize": "^0.0.30", "@types/react": "~19.0.10", "@types/react-native-vector-icons": "^6.4.12", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "babel-plugin-module-resolver": "^5.0.0", "eslint": "^7.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-react": "^7.21.5", "husky": ">=6", "lint-staged": ">=10", "prettier": "^2.3.1", "typescript": "~5.8.3"}, "private": true, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{ts,tsx}": "eslint"}}