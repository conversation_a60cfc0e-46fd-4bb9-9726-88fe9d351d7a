import React from 'react';
import { Button } from '@ui-kitten/components';
import { RightArrowIcon } from '~components/Icon';
import LinkForOtherPerson from '~components/Housekeeping/ChecklistTab/LinkForOtherPerson';
import { HKChecklist, HKExtendedJob } from '~/types';
import Stack from '~components/Housekeeping/UI/Stack';
import StickyHeaderModal from '~components/Housekeeping/StickyHeaderModal';

type Props = {
  job: HKExtendedJob;
  checklists: HKChecklist[] | null;
  opened: boolean;
  onClose: () => void;
};

const LinkForOtherPersonModal = ({
  job,
  checklists,
  opened,
  onClose,
}: Props) => {
  if (!checklists) {
    return null;
  }

  return (
    <StickyHeaderModal
      visible={opened}
      onClose={onClose}
      title="Share Checklist"
    >
      <Stack>
        {checklists.map(checklist => (
          <LinkForOtherPerson key={checklist} job={job} checklist={checklist} />
        ))}
        <Button
          status="info"
          size="large"
          accessoryRight={RightArrowIcon}
          onPress={onClose}
        >
          Continue
        </Button>
      </Stack>
    </StickyHeaderModal>
  );
};

export default LinkForOtherPersonModal;
