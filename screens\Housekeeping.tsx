import React, { useLayoutEffect } from 'react';
import { Text } from '@ui-kitten/components';
import { Platform } from 'react-native';
import { openURL } from 'expo-linking';
import { useMutation, useQueryClient } from 'react-query';
import { StackScreenProps } from '@react-navigation/stack';
import HousekeepingPage from '~components/Housekeeping/HousekeepingPage';
import { MyTasksParamList, HKJob, HKJobSection, HKPerformType } from '~types';
import {
  whoAmI,
  pdfReportPath,
  getUserFromChecklistToken,
} from '~helpers/hk-helpers';
import useHkApi from '~api/useHkApi';
import useHKItems from '~queries/useHKItems';
import useHKSections from '~queries/useHKSections';
import useHKJob from '~queries/useHKJob';
import LanguageSelector from '~components/Housekeeping/LangulageSelector/LanguageSelector';

const HeaderRight = () => <LanguageSelector />;

export type HousekeepingScreenProps = StackScreenProps<
  MyTasksParamList,
  'HousekeepingScreen'
>;

const HousekeepingScreen = ({ route, navigation }: HousekeepingScreenProps) => {
  const {
    performer,
    checklistToken,
    propertyId,
    taskAssignmentId,
    jobId = 0,
  } = route.params || {};

  const queryClient = useQueryClient();
  const { addJob, completeJobSection, startChecklist, fetchJob } = useHkApi();

  const {
    job,
    isLoading: jobIsLoading,
    refetch: refetchHKJob,
  } = useHKJob({
    jobId,
    checklistToken,
  });
  const { items: tasks, isLoading: itemsIsLoading } = useHKItems(
    !!propertyId || !!jobId || !!checklistToken,
    checklistToken,
  );
  const { sections, isLoading: sectionsIsLoading } = useHKSections(
    !!propertyId || !!jobId || !!checklistToken,
    checklistToken,
  );

  const { mutateAsync: addJobMutate } = useMutation(addJob);

  const { mutateAsync: startChecklistMutate } = useMutation(startChecklist, {
    onSuccess: () => {
      queryClient.invalidateQueries(['job', jobId, checklistToken]);
    },
  });
  const { mutateAsync: updateJobSectionMutate } = useMutation(
    completeJobSection,
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['job', jobId, checklistToken]);
      },
    },
  );

  const isLoading = jobIsLoading || itemsIsLoading || sectionsIsLoading;

  useLayoutEffect(() => {
    navigation.setOptions({
      headerShown: true,
      headerTitle: 'Housekeeping',
      headerRight: HeaderRight,
    });
  }, [navigation]);

  if (
    (!jobId && !checklistToken && (!propertyId || !taskAssignmentId)) ||
    (!isLoading && (!tasks || !sections))
  ) {
    return <Text style={{ color: 'red', padding: 30 }}>Access Denied.</Text>;
  }

  if (!isLoading && (!propertyId || !taskAssignmentId) && !job) {
    return (
      <Text style={{ color: 'red', padding: 30 }}>
        This housekeeping job has not been created for this property.
      </Text>
    );
  }

  const user = checklistToken
    ? getUserFromChecklistToken(job, checklistToken)
    : whoAmI(job, performer === 'helper');
  const { performType } = job || {};

  const handleSaveAssignRolesForm = async (values: HKJob) => {
    if ((!propertyId || !taskAssignmentId) && !job) return;
    const updatedJob = await addJobMutate({
      job: values,
      taskAssignmentId: taskAssignmentId || job!.taskAssignmentId,
      propertyId: propertyId || job!.propertyId,
    });

    if (!values.id) {
      queryClient.invalidateQueries(['task-assignments', taskAssignmentId]);
      navigation.setParams({
        propertyId: undefined,
        taskAssignmentId: undefined,
        jobId: updatedJob.id,
      });
      return;
    }

    if (checklistToken) {
      const fetchedJob = await fetchJob(updatedJob.id);
      const checklistTokenIndex =
        fetchedJob.leaderPerforms === 'all' ||
        (user?.role === 'leader' && fetchedJob.leaderPerforms === 'person1') ||
        (user?.role === 'helper' && fetchedJob.leaderPerforms === 'person2')
          ? 0
          : 1;
      const updatedChecklistToken =
        fetchedJob.checklistTokens[checklistTokenIndex].token;

      navigation.setParams({ checklistToken: updatedChecklistToken });
    } else {
      refetchHKJob();
    }
  };

  const handleClickPDFReport = () => {
    if (!job) return;
    const url = pdfReportPath(jobId || job.id);
    if (Platform.OS === 'web') {
      window.open(url, '_blank');
    } else {
      openURL(url);
    }
  };

  const handleStartChecklist = async () => {
    if (!job || !user?.performing) return;
    await startChecklistMutate({
      jobId: jobId || job.id,
      checklist: user.performing,
      checklistToken,
    });
  };

  const handleCompleteSection = async (
    jobSection: Omit<HKJobSection, 'completedAt'>,
  ) => {
    if (!jobSection) return;
    await updateJobSectionMutate({ jobSection, checklistToken });
  };

  const handleClickFinish = async () => {
    if (!job?.taskAssignmentId) return;
    navigation.navigate('FinishTaskScreen', {
      taskAssignmentId: job.taskAssignmentId,
    });
  };

  return (
    <HousekeepingPage
      job={job}
      user={user}
      isLoading={isLoading}
      sections={sections || []}
      onStartChecklist={handleStartChecklist}
      onCompleteSection={handleCompleteSection}
      tasks={tasks || []}
      onClickFinish={!checklistToken ? handleClickFinish : undefined}
      onClickPDFReport={!checklistToken ? handleClickPDFReport : null}
      pdfReportPath={
        !checklistToken ? pdfReportPath(jobId || job?.id || 0) : undefined
      }
      onSaveAssignRolesForm={
        !checklistToken ? handleSaveAssignRolesForm : undefined
      }
      updateJob={refetchHKJob}
      canEditRoles={!checklistToken}
      onlyAssigning={
        !checklistToken ? performType === HKPerformType.Assigned : undefined
      }
    />
  );
};

export default HousekeepingScreen;
