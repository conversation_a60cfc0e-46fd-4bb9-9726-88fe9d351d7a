import React, { useEffect, useState } from 'react';
import { Button } from '@ui-kitten/components';
import { Alert } from 'react-native';
import * as Sharing from 'expo-sharing';
import CopyButton from '~components/Housekeeping/CopyButton';

const COPY_TIMEOUT = 5000;

type Props = {
  url: string;
  disabled: boolean;
  onClickShare: () => void;
  shared: boolean;
};

const SharePDFButton = ({ url, disabled, onClickShare, shared }: Props) => {
  const [isSharingAvailable, setIsSharingAvailable] = useState(false);

  useEffect(() => {
    (async () => {
      setIsSharingAvailable(await Sharing.isAvailableAsync());
    })();
  }, []);

  if (isSharingAvailable) {
    const onShare = async () => {
      try {
        await Sharing.shareAsync(url, {
          dialogTitle: 'PDF Report',
          UTI: 'com.adobe.pdf',
        });
        onClickShare();
      } catch (error: unknown) {
        if (error instanceof Error) {
          Alert.alert(error.message);
        }
      }
    };

    return (
      <Button
        size="large"
        style={{ width: '100%' }}
        onPress={onShare}
        disabled={disabled}
        appearance={shared ? 'outline' : 'filled'}
        status="info"
      >
        Share PDF Report
      </Button>
    );
  }

  return (
    <CopyButton
      href={url}
      timeout={COPY_TIMEOUT}
      onClickShare={onClickShare}
      disabled={disabled}
      copyText="Share PDF Report"
      copiedText="Link Copied to Clipboard"
      size="large"
      shareIcons={false}
    />
  );
};

export default SharePDFButton;
