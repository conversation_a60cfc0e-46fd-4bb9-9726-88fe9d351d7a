import React from 'react';
import {
  Button,
  IconElement,
  IconProps,
  StyleService,
  Text,
  useStyleSheet,
} from '@ui-kitten/components';

const HeaderButton = ({
  onClick,
  icon: IconTag,
  label,
}: {
  onClick: () => void;
  icon: (style: IconProps) => IconElement;
  label: string;
}) => {
  const styles = useStyleSheet(themedStyles);

  return (
    <Button
      style={styles.button}
      onPress={onClick}
      appearance="outline"
      status="info"
      accessoryLeft={IconTag}
    >
      <Text>{label}</Text>
    </Button>
  );
};

export default HeaderButton;

const themedStyles = StyleService.create({
  button: {
    flex: 1,
    paddingHorizontal: 14,
    height: 30,
    paddingVertical: 0,
    minHeight: 0,
  },
});
