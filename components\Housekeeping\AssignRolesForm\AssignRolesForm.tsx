import React, { useState } from 'react';
import * as Yup from 'yup';
import { StyleService, useStyleSheet } from '@ui-kitten/components';
import { Formik } from 'formik';
import { RightArrowIcon } from '~/components/Icon';
import { HKJob, HKChecklist, HKSection, HKTask, HKPerformType } from '~/types';
import useHousekeepingPerformOptions from '~hooks/useHkPerformOptions';
import SpinnerButton from '~components/SpinnerButton';
import RadioCardGroup from '~components/Housekeeping/RadioCardGroup';
import AssignRoleInput from '~components/Housekeeping/AssignRolesForm/AssignRoleInput';
import KeyboardAvoidingView from '~components/KeyboardAvoidingView';
import PreviewButton from '~components/Housekeeping/AssignRolesForm/PreviewButton';
import ChecklistSectionsModal from '~components/Housekeeping/AssignRolesForm/ChecklistSectionsModal';
import CHECKLIST_OPTIONS from '~constants/ChecklistOptions';

const schema = Yup.object().shape({
  performType: Yup.string().required('This field is required'),
  leaderPerforms: Yup.string().required('This field is required'),
  leaderName: Yup.string().required('This field is required'),
  helperName: Yup.string().when('leaderPerforms', {
    is: 'all',
    then: Yup.string(),
    otherwise: Yup.string().required('Required'),
  }),
});

type Props = {
  onSave: (values: HKJob) => Promise<void>;
  values?: HKJob | null;
  sections?: HKSection[];
  tasks?: HKTask[];
};

const AssignRolesForm = ({ values, sections, tasks, onSave }: Props) => {
  const styles = useStyleSheet(themedStyle);
  const [checklistPreview, setChecklistPreview] = useState<HKChecklist | null>(
    null,
  );
  const personalOptions = useHousekeepingPerformOptions({
    performPersonally: true,
  });
  const assignedOptions = useHousekeepingPerformOptions({
    performPersonally: false,
  });

  const onSubmit = (submitValues: HKJob) => onSave(submitValues);

  const handleClickPreview = (checklist: HKChecklist) => {
    setChecklistPreview(checklist);
  };
  const handleCloseChecklistPreview = () => {
    setChecklistPreview(null);
  };

  const showPreview = tasks && sections;

  return (
    <>
      <Formik
        initialValues={
          values ?? {
            id: null,
            performType: HKPerformType.Personally,
            leaderPerforms: 'person1',
            leaderName: '',
            helperName: '',
          }
        }
        onSubmit={onSubmit}
        validationSchema={schema}
        enableReinitialize
        validateOnMount
      >
        {({
          values: { performType, leaderPerforms, leaderName, helperName },
          setFieldValue,
          handleSubmit,
          isValid,
          isSubmitting,
        }) => {
          const performPersonally = performType === HKPerformType.Personally;
          const spinnerText = values || !performPersonally ? 'Save' : 'Start';
          const performOptions = performPersonally
            ? personalOptions
            : assignedOptions;

          return (
            <KeyboardAvoidingView contentContainerStyle={styles.container}>
              <RadioCardGroup
                name="performType"
                formLabel="Will you be the one performing the checklist personally?"
                options={CHECKLIST_OPTIONS}
                onSelect={index => {
                  setFieldValue(
                    'performType',
                    index === 0
                      ? HKPerformType.Personally
                      : HKPerformType.Assigned,
                  );
                }}
              />
              <RadioCardGroup
                name="leaderPerforms"
                formLabel={
                  performPersonally
                    ? 'Which checklist are you going to follow?'
                    : 'Which checklist is the lead housekeeper going to follow?'
                }
                options={performOptions.map(performOption => ({
                  ...performOption,
                  optionChildren: showPreview && (
                    <PreviewButton
                      onPress={() => handleClickPreview(performOption.value)}
                    />
                  ),
                }))}
                onSelect={index => {
                  setFieldValue('leaderPerforms', performOptions[index].value);
                }}
              />
              <AssignRoleInput
                label={
                  performPersonally
                    ? "What's your name?"
                    : "What's the lead housekeeper's name?"
                }
                value={leaderName}
                onChangeText={value => setFieldValue('leaderName', value)}
                placeholder={
                  performPersonally
                    ? 'Enter your name'
                    : "Enter the lead housekeeper's name"
                }
              />
              {leaderPerforms !== 'all' && (
                <AssignRoleInput
                  label="What's the name of the other person?"
                  value={helperName}
                  onChangeText={value => setFieldValue('helperName', value)}
                  placeholder="Enter the name of the other person"
                />
              )}
              <SpinnerButton
                text={!isSubmitting ? spinnerText : ''}
                accessoryRight={!isSubmitting ? RightArrowIcon : undefined}
                onPress={() => handleSubmit()}
                isLoading={isSubmitting}
                disabled={!isValid}
                status="info"
              />
            </KeyboardAvoidingView>
          );
        }}
      </Formik>
      {showPreview && !!checklistPreview && (
        <ChecklistSectionsModal
          sections={sections}
          tasks={tasks}
          checklist={checklistPreview}
          onClose={handleCloseChecklistPreview}
        />
      )}
    </>
  );
};

export default AssignRolesForm;

const themedStyle = StyleService.create({
  container: {
    gap: 24,
  },
});
