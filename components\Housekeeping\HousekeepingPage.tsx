import React, { ReactNode, useContext, useMemo, useState } from 'react';
import { Text, StyleService, useStyleSheet } from '@ui-kitten/components';
import { ScrollView, View } from 'react-native';
import {
  HKExtendedJob,
  HKUser,
  HKSection,
  HKJobSection,
  HKTask,
  HKChecklist,
  HKJob,
  HKPerformType,
} from '~types';
import {
  getActiveSection,
  getAllRemainingSections,
  getChecklistsToShare,
} from '~helpers/hk-helpers';
import AssignRolesForm from '~components/Housekeeping/AssignRolesForm/AssignRolesForm';
import LinkForOtherPersonModal from '~components/Housekeeping/ChecklistTab/LinkForOtherPersonModal';
import DatesAndPropertyInfo from '~components/Housekeeping/ChecklistTab/DatesAndPropertyInfo';
import ShareAndFinishButtons from '~components/Housekeeping/ChecklistTab/ShareAndFinishButtons';
import HeaderButtons from '~components/Housekeeping/ChecklistTab/HeaderButtons';
import AssignRolesModal from '~components/Housekeeping/ChecklistTab/AssignRolesModal';
import AccessInfoListModal from '~components/Housekeeping/ChecklistTab/AccessInfoListModal';
import ChecklistTabs from '~components/Housekeeping/ChecklistTab/ChecklistTabs';
import GeoLocationAlert from '~components/Housekeeping/ChecklistTab/GeoLocationAlert';
import Spinner from '~components/Spinner';
import useUser from '~queries/useUser';
import useTaskAssignment from '~hooks/useTaskAssignment';
import AuthContext from '~context/AuthContext';

type Props = {
  job: HKExtendedJob | undefined;
  sections: HKSection[];
  onStartChecklist: () => Promise<void>;
  onCompleteSection: (
    jobSection: Omit<HKJobSection, 'completedAt'>,
  ) => Promise<void>;
  tasks: HKTask[];
  isLoading: boolean;
  updateJob: () => void;
  user: HKUser | null;
  canEditRoles: boolean;
  onClickPDFReport: (() => void) | null;
  onSaveAssignRolesForm?: (job: HKJob) => Promise<void>;
  onClickFinish?: () => void;
  pdfReportPath?: string;
  onlyAssigning?: boolean;
};

const HousekeepingPage = ({
  job,
  sections,
  onStartChecklist,
  onCompleteSection,
  tasks,
  isLoading,
  updateJob,
  user,
  canEditRoles,
  onSaveAssignRolesForm,
  onClickPDFReport,
  onClickFinish,
  pdfReportPath,
  onlyAssigning = false,
}: Props) => {
  const { authToken } = useContext(AuthContext);
  const styles = useStyleSheet(themedStyles);

  const [geoLocationIsEnabled, setGeoLocationIsEnabled] =
    useState<boolean>(false);
  const [checklistsToShare, setChecklistsToShare] = useState<
    HKChecklist[] | null
  >(null);
  const [accessInfoDrawerOpened, setAccessInfoListModalOpened] =
    useState(false);
  const [assignRolesModalOpened, setAssignRolesModalOpened] = useState(false);
  const [selectedTab, setSelectedTab] = useState<HKChecklist>('person1');
  const [stickyHeader, setStickyHeader] = useState<ReactNode | null>(null);

  const performing = user?.performing ?? null;
  const isLeader = !job || user?.role === 'leader';
  const jobPerformType = job?.performType;
  const remainingSections = getAllRemainingSections(performing, sections, job);
  const activeSection = getActiveSection(performing, sections, job);

  const { data: currentUser } = useUser({
    options: { enabled: !!authToken },
  });
  const { data: taskAssignment } = useTaskAssignment({
    id: job?.taskAssignmentId || 0,
    options: { enabled: !!currentUser && !!job?.taskAssignmentId },
  });

  const ownsHKJob = useMemo(
    () =>
      isLeader &&
      taskAssignment &&
      taskAssignment.housekeepingChecklistJob?.id === job?.id,
    [isLeader, taskAssignment, job?.id],
  );

  if (!geoLocationIsEnabled) {
    return (
      <GeoLocationAlert
        enabled={geoLocationIsEnabled}
        onChange={setGeoLocationIsEnabled}
      />
    );
  }

  if (isLoading) {
    return <Spinner />;
  }

  const handleClickRoles = canEditRoles
    ? () => setAssignRolesModalOpened(true)
    : null;
  const handleClickPropertyAccess = isLeader
    ? () => setAccessInfoListModalOpened(true)
    : null;
  const handleCloseLinkForOtherPersonModal = () => setChecklistsToShare(null);
  const handleCloseAccessInfoListModal = () =>
    setAccessInfoListModalOpened(false);
  const handleUpdateJob = () => updateJob();
  const handleSaveAssignRolesForm = async ({
    id,
    leaderPerforms,
    leaderName,
    helperName,
    performType,
  }: HKJob) => {
    if (!onSaveAssignRolesForm) return;
    await onSaveAssignRolesForm({
      id,
      leaderPerforms,
      leaderName,
      helperName,
      performType,
    });
    setStickyHeader(null);
    setAssignRolesModalOpened(false);
    setSelectedTab(leaderPerforms ?? 'person1');
    if (performType === HKPerformType.Assigned || leaderPerforms !== 'all') {
      setChecklistsToShare(getChecklistsToShare(performType, leaderPerforms));
    }
  };

  if (!job) {
    return (
      <ScrollView
        contentContainerStyle={[
          styles.container,
          styles.assignRoleFormContainer,
        ]}
      >
        <Text category="p1">
          First you will need to select if you are performing the checklist by
          yourself or with a team member or assign this job completely to other
          people.
        </Text>
        <AssignRolesForm
          onSave={handleSaveAssignRolesForm}
          sections={sections}
          tasks={tasks}
        />
      </ScrollView>
    );
  }
  return (
    <>
      <ScrollView
        stickyHeaderIndices={stickyHeader ? [0] : []}
        contentContainerStyle={styles.container}
      >
        {stickyHeader && (
          <View style={styles.stickyHeaderContainer}>{stickyHeader}</View>
        )}
        <DatesAndPropertyInfo job={job} />
        {(isLeader || jobPerformType === HKPerformType.Assigned) && (
          <HeaderButtons
            onClickPropertyAccess={handleClickPropertyAccess}
            onClickRoles={handleClickRoles}
            onClickReport={onClickPDFReport}
          />
        )}
        <ChecklistTabs
          job={job}
          user={user}
          onStartChecklist={onStartChecklist}
          onCompleteSection={onCompleteSection}
          tasks={tasks}
          performing={performing}
          updateJob={handleUpdateJob}
          isLeader={isLeader}
          selectedTab={selectedTab}
          setSelectedTab={setSelectedTab}
          sections={sections}
          activeSection={activeSection}
          onlyAssigning={onlyAssigning}
          setStickyHeader={setStickyHeader}
        />
        <LinkForOtherPersonModal
          job={job}
          checklists={checklistsToShare}
          opened={!!checklistsToShare?.length}
          onClose={handleCloseLinkForOtherPersonModal}
        />
        <AccessInfoListModal
          job={job}
          opened={accessInfoDrawerOpened}
          onClose={handleCloseAccessInfoListModal}
        />
        {canEditRoles && (
          <AssignRolesModal
            values={job}
            opened={assignRolesModalOpened}
            onClose={() => setAssignRolesModalOpened(false)}
            onSave={handleSaveAssignRolesForm}
          />
        )}
      </ScrollView>
      {ownsHKJob && onClickFinish && pdfReportPath && (
        <ShareAndFinishButtons
          style={styles.finishButtonContainer}
          onClickFinish={onClickFinish}
          disabled={remainingSections.length > 0}
          pdfReportPath={pdfReportPath}
        />
      )}
    </>
  );
};

export default HousekeepingPage;

const themedStyles = StyleService.create({
  stickyHeaderContainer: {
    backgroundColor: 'white',
    paddingHorizontal: 12,
  },
  assignRoleFormContainer: {
    paddingHorizontal: 12,
  },
  geolocation: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    gap: 16,
    padding: 12,
    backgroundColor: 'white',
  },
  finishButtonContainer: {
    paddingHorizontal: 12,
  },
});
