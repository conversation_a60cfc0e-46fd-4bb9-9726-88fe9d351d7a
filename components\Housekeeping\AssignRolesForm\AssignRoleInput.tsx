import React from 'react';
import { Input } from '@ui-kitten/components';
import Stack from '~components/Housekeeping/UI/Stack';
import AssignRoleLabel from '~components/Housekeeping/AssignRolesForm/AssignRoleLabel';

const AssignRoleInput = ({
  label,
  value,
  placeholder,
  onChangeText,
}: {
  label: string;
  value: string;
  placeholder: string;
  onChangeText: (text: string) => void;
}) => (
  <Stack style={{ gap: 12 }}>
    <AssignRoleLabel label={label} />
    <Input
      value={value}
      onChangeText={onChangeText}
      placeholder={placeholder}
      status="info"
    />
  </Stack>
);

export default AssignRoleInput;
