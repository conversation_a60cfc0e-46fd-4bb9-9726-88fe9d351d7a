import React, { useCallback, useEffect, useState } from 'react';
import {
  <PERSON>ton,
  Card,
  Layout,
  StyleService,
  Text,
  useStyleSheet,
} from '@ui-kitten/components';
import { reloadAsync } from 'expo-updates';
import { View, Platform, DevSettings } from 'react-native';
import Spinner from '~components/Spinner';
import Stack from '~components/Housekeeping/UI/Stack';
import { getLocationPermission } from '~helpers/hk-helpers';

type Props = {
  enabled: boolean;
  onChange: (enabled: boolean) => void;
};

const GeoLocationAlert = ({ enabled, onChange }: Props) => {
  const styles = useStyleSheet(themedStyles);
  const [isLoading, setIsLoading] = useState(true);

  const handleChange = useCallback(
    (isEnabled: boolean) => {
      setIsLoading(false);
      onChange(isEnabled);
    },
    [onChange],
  );

  const handleClickReload = async () => {
    if (Platform.OS === 'web') {
      window.location.reload();
    } else if (__DEV__) {
      DevSettings.reload();
    } else {
      await reloadAsync();
    }
  };

  useEffect(() => {
    (async () => {
      const isEnabled = await getLocationPermission();
      handleChange(isEnabled);
    })();
  }, [handleChange, onChange]);

  if (isLoading) {
    return <Spinner />;
  }

  if (enabled) return null;

  const cardHeader = () => (
    <View style={styles.header}>
      <Text category="h6">Geolocation is disabled</Text>
    </View>
  );

  return (
    <Layout style={styles.container} level="1">
      <Card disabled header={cardHeader}>
        <Stack>
          <Text category="s1">
            Please enable geolocation and reload page to continue
          </Text>
          <Button onPress={handleClickReload} style={styles.button}>
            Reload
          </Button>
        </Stack>
      </Card>
    </Layout>
  );
};

export default GeoLocationAlert;

const themedStyles = StyleService.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  header: {
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  button: {
    alignSelf: 'flex-end',
  },
});
