import React from 'react';
import { HKChecklist, HKSection, HKTask } from '~/types';
import ChecklistSections from '~components/Housekeeping/ChecklistTab/ChecklistSections';
import StickyHeaderModal from '~components/Housekeeping/StickyHeaderModal';
import { formatPreviewName } from '~helpers/hk-helpers';

type Props = {
  sections: HKSection[];
  tasks: HKTask[];
  checklist: HKChecklist;
  onClose: () => void;
};

const ChecklistSectionsModal = ({
  sections,
  tasks,
  checklist,
  onClose,
}: Props) => (
  <StickyHeaderModal
    visible={!!checklist}
    onClose={onClose}
    title={`Preview ${formatPreviewName(checklist)}`}
  >
    <ChecklistSections
      sections={sections}
      checklist={checklist}
      tasks={tasks.filter(task => task[checklist])}
    />
  </StickyHeaderModal>
);

export default ChecklistSectionsModal;
