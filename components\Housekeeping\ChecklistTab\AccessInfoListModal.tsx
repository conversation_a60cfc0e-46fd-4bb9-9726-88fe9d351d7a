import React from 'react';
import { StyleService, Button } from '@ui-kitten/components';
import { Modal, View } from 'react-native';
import { HKExtendedJob } from '~/types';
import PropertyCard from '~components/PropertyCard';
import { formatDateWithTimezone } from '~helpers/hk-helpers';

type Props = {
  job: HKExtendedJob | null;
  opened: boolean;
  onClose: () => void;
};

const AccessInfoListModal = ({ job, opened, onClose }: Props) => {
  if (!job || !job.id) return null;
  const timeZone =
    job?.jobSections[0]?.location?.timezone || 'America/Los_Angeles';

  const {
    id,
    property: {
      name,
      address,
      accessInformation,
      notes,
      deletedAt,
      isSuspended,
    },
  } = job;

  const deletedAtWithTimezone = deletedAt
    ? formatDateWithTimezone(deletedAt, timeZone)
    : undefined;

  return (
    <Modal visible={opened} onRequestClose={onClose}>
      <View style={styles.modal}>
        <PropertyCard
          id={id}
          name={name}
          address={address}
          accessInformation={accessInformation}
          notes={notes}
          deletedAt={deletedAtWithTimezone}
          isSuspended={isSuspended}
          showDetails
        />
        <Button size="large" onPress={onClose} style={styles.button}>
          Close
        </Button>
      </View>
    </Modal>
  );
};

export default AccessInfoListModal;

const styles = StyleService.create({
  modal: {
    paddingVertical: 42,
    backgroundColor: 'white',
    height: '100%',
  },
  button: {
    marginTop: 'auto',
  },
});
