import { useQuery, UseQueryOptions, QueryKey } from '~node_modules/react-query';
import useTasks from '~api/useTasks';
import { TaskAssignment } from '~types';

type Props = {
  id: TaskAssignment['id'];
  options?: Omit<
    UseQueryOptions<TaskAssignment, unknown, TaskAssignment, QueryKey>,
    'queryKey' | 'queryFn'
  >;
};
const useTaskAssignment = ({ id, options }: Props) => {
  const { getTaskAssignment } = useTasks();

  return useQuery(
    ['task-assignments', id],
    () => getTaskAssignment(id),
    options,
  );
};

export default useTaskAssignment;
