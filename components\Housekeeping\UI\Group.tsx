import React, { ComponentProps } from 'react';
import { View } from 'react-native';
import { StyleService, useStyleSheet } from '@ui-kitten/components';

type Props = ComponentProps<typeof View>;

const Group = ({ style, ...props }: Props) => {
  const styles = useStyleSheet(themedStyles);

  return <View style={[styles.group, style]} {...props} />;
};

export default Group;

const themedStyles = StyleService.create({
  group: {
    gap: 16,
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
});
