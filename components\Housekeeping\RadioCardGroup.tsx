import React, { ReactNode } from 'react';
import {
  Text,
  useStyleSheet,
  StyleService,
  Radio,
  RadioGroup,
} from '@ui-kitten/components';
import { View } from 'react-native';
import { Field, FormikValues } from 'formik';
import Group from '~components/Housekeeping/UI/Group';
import Flex from '~components/Housekeeping/UI/Flex';
import AssignRoleLabel from '~components/Housekeeping/AssignRolesForm/AssignRoleLabel';
import Stack from '~components/Housekeeping/UI/Stack';

type Props = {
  options: Array<{
    value: string | number;
    label: string;
    description?: string;
    icon?: ReactNode;
    optionChildren?: ReactNode;
  }>;
  name: string;
  formLabel: string;
  onSelect: (index: number) => void;
};

const RadioCardGroup = ({ options, name, formLabel, onSelect }: Props) => {
  const styles = useStyleSheet(themedStyles);

  return (
    <Field>
      {({ form: { values, setFieldValue } }: FormikValues) => {
        const selectedIndex = options.findIndex(
          field => field.value === values[name],
        );
        return (
          <Stack style={{ gap: 12 }}>
            <AssignRoleLabel label={formLabel} />
            <RadioGroup
              style={styles.radioGroup}
              selectedIndex={selectedIndex}
              onChange={(index: number) => {
                const { value } = options[index];
                onSelect?.(index);
                setFieldValue(name, value);
              }}
            >
              {options.map(
                (
                  { value, label, description, icon, optionChildren },
                  index,
                ) => (
                  <Radio key={value} style={styles.radio}>
                    {() => (
                      <Group
                        style={[
                          styles.content,
                          index === selectedIndex ? styles.selected : {},
                        ]}
                      >
                        {icon && (
                          <View style={styles.iconContainer}>{icon}</View>
                        )}
                        <Flex style={styles.textContainer}>
                          <Text category="p2" style={styles.label}>
                            {label}
                          </Text>
                          <Text
                            category="c1"
                            appearance="hint"
                            style={styles.description}
                          >
                            {description}
                          </Text>
                          {optionChildren}
                        </Flex>
                      </Group>
                    )}
                  </Radio>
                ),
              )}
            </RadioGroup>
          </Stack>
        );
      }}
    </Field>
  );
};

const themedStyles = StyleService.create({
  radioGroup: {
    gap: 16,
  },
  content: {
    flex: 1,
    flexWrap: 'nowrap',
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'border-basic-color-5',
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
  },
  radio: {
    marginVertical: 0,
    marginLeft: -20,
  },
  iconContainer: {
    width: 40,
    flexShrink: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  textContainer: {
    flex: 1,
    flexDirection: 'column',
    gap: 8,
  },
  label: {
    fontWeight: '700',
    fontSize: 16,
  },
  description: {
    flex: 1,
  },
  selected: {
    borderColor: 'color-info-500',
  },
});

export default RadioCardGroup;
