import React from 'react';
import {
  Icon,
  StyleService,
  useStyleSheet,
  useTheme,
} from '@ui-kitten/components';
import { ClockOutlineIcon, PinIcon } from '~/components/Icon';
import Badge from '~components/Housekeeping/UI/Badge';
import Stack from '~components/Housekeeping/UI/Stack';
import Flex from '~components/Housekeeping/UI/Flex';

type Props = {
  completedAtWithTimezone: string | null;
  completedIn: string;
  totalTimeElapsed: string;
  showTotal: boolean;
  address: string | undefined;
};

const CompletedSectionBadges = ({
  completedAtWithTimezone,
  completedIn,
  totalTimeElapsed,
  showTotal,
  address,
}: Props) => {
  const styles = useStyleSheet(themedStyles);
  const theme = useTheme();

  return (
    <Flex style={styles.completedBadges}>
      <Icon
        name="checkmark-circle-2"
        fill={theme['color-success-600']}
        width={70}
        style={styles.icon}
      />
      <Stack style={styles.badgeGroup}>
        {completedAtWithTimezone && (
          <Badge
            status="success"
            size="large"
            style={styles.badge}
            leftSection={ClockOutlineIcon}
          >
            {completedAtWithTimezone}
          </Badge>
        )}
        {completedIn && (
          <Badge
            status="success"
            size="large"
            style={styles.badge}
            leftSection={ClockOutlineIcon}
          >
            Section: {completedIn}
          </Badge>
        )}
        {showTotal && (
          <Badge
            status="success"
            size="large"
            style={styles.badge}
            leftSection={ClockOutlineIcon}
          >
            Total: {totalTimeElapsed}
          </Badge>
        )}
        {address && (
          <Badge size="large" style={styles.badge} leftSection={PinIcon}>
            {address}
          </Badge>
        )}
      </Stack>
    </Flex>
  );
};

export default CompletedSectionBadges;

const themedStyles = StyleService.create({
  completedBadges: {
    gap: 12,
    padding: 5,
    alignItems: 'center',
    flexWrap: 'nowrap',
  },
  badgeGroup: {
    flex: 1,
  },
  icon: {
    flexShrink: 0,
    height: '100%',
    aspectRatio: 1,
  },
  badge: {
    paddingLeft: 5,
  },
});
