import React, { useState } from 'react';
import SpinnerButton from '~components/SpinnerButton';

type Props = {
  onStartChecklist: () => Promise<void>;
};

const StartChecklistButton = ({ onStartChecklist }: Props) => {
  const [loading, setLoading] = useState(false);

  const handleStartChecklist = async () => {
    setLoading(true);
    await onStartChecklist();
    setLoading(false);
  };

  return (
    <SpinnerButton
      onPress={handleStartChecklist}
      size="large"
      style={{ width: '100%' }}
      isLoading={loading}
      text="Start Checklist"
      status="info"
    />
  );
};

export default StartChecklistButton;
