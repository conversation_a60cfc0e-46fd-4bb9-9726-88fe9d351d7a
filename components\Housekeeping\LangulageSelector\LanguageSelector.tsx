import React, { useState } from 'react';
import {
  IndexPath,
  Select,
  SelectItem,
  StyleService,
} from '@ui-kitten/components';
import { useNavigation, useRoute } from '@react-navigation/native';
import CountryFlag from 'react-native-country-flag';
import { HousekeepingScreenProps } from '~screens/Housekeeping';
import { languageOptions } from '~helpers/hk-helpers';
import useRouteLanguage from '~hooks/useRouteLanguage';

const LanguageSelector = () => {
  const navigation = useNavigation<HousekeepingScreenProps['navigation']>();
  const route = useRoute<HousekeepingScreenProps['route']>();
  const routeLanguage = useRouteLanguage();
  const [selectedIndex, setSelectedIndex] = useState<IndexPath>(
    new IndexPath(
      languageOptions.findIndex(({ code }) => code === routeLanguage),
    ),
  );

  const handleSelect = (index: IndexPath | IndexPath[]) => {
    if (Array.isArray(index)) return;

    setSelectedIndex(index);
    const { code } = languageOptions[index.row];
    navigation.navigate(route.name, { ...route.params, lang: code });
  };

  return (
    <Select
      style={styles.select}
      value={languageOptions[selectedIndex.row].name}
      accessoryLeft={() =>
        CountryFlag({
          isoCode: languageOptions[selectedIndex.row].flagCode,
          size: 12,
        })
      }
      selectedIndex={selectedIndex}
      onSelect={handleSelect}
    >
      {languageOptions.map(({ name, flagCode }) => (
        <SelectItem
          key={flagCode}
          accessoryLeft={() =>
            CountryFlag({
              isoCode: flagCode,
              size: 12,
            })
          }
          title={name}
        />
      ))}
    </Select>
  );
};

export default LanguageSelector;

const styles = StyleService.create({
  select: { marginHorizontal: 16 },
});
